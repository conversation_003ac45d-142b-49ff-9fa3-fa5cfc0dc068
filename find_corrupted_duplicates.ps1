# البحث عن الملفات التالفة والمستنسخة في مشروع Azkary
Write-Host "Searching for corrupted and duplicate files in Azkary project..." -ForegroundColor Green

$projectPath = "C:\Users\<USER>\Documents\augment-projects\azkary\azkary"

if (-not (Test-Path $projectPath)) {
    Write-Host "Azkary project not found at specified path" -ForegroundColor Red
    exit
}

Write-Host "Analyzing project: $projectPath" -ForegroundColor Yellow

# البحث عن الملفات المستنسخة (نفس الاسم مع أرقام)
Write-Host "`n=== Searching for duplicate files ===" -ForegroundColor Cyan

$duplicatePatterns = @(
    "*copy*",
    "*Copy*",
    "*COPY*",
    "* - Copy*",
    "* (1)*",
    "* (2)*",
    "* (3)*",
    "*_backup*",
    "*_old*",
    "*_temp*",
    "*~*"
)

$duplicatesFound = @()
foreach ($pattern in $duplicatePatterns) {
    $files = Get-ChildItem -Path $projectPath -Recurse -Name $pattern -ErrorAction SilentlyContinue
    foreach ($file in $files) {
        $fullPath = Join-Path $projectPath $file
        $size = (Get-Item $fullPath -ErrorAction SilentlyContinue).Length
        $sizeMB = [math]::Round($size / 1MB, 2)
        
        $duplicatesFound += [PSCustomObject]@{
            Path = $file
            FullPath = $fullPath
            Size = $size
            SizeMB = $sizeMB
        }
        
        Write-Host "  Found duplicate: $file ($sizeMB MB)" -ForegroundColor Yellow
    }
}

# البحث عن ملفات بنفس المحتوى (hash)
Write-Host "`n=== Searching for files with identical content ===" -ForegroundColor Cyan

$allFiles = Get-ChildItem -Path $projectPath -Recurse -File -ErrorAction SilentlyContinue | 
    Where-Object { $_.Length -gt 0 -and $_.Length -lt 100MB }

$hashGroups = @{}
$identicalFiles = @()

Write-Host "Calculating file hashes... (this may take a moment)" -ForegroundColor Gray

foreach ($file in $allFiles) {
    try {
        $hash = Get-FileHash -Path $file.FullName -Algorithm MD5 -ErrorAction SilentlyContinue
        if ($hash) {
            if (-not $hashGroups.ContainsKey($hash.Hash)) {
                $hashGroups[$hash.Hash] = @()
            }
            $hashGroups[$hash.Hash] += $file
        }
    }
    catch {
        # Skip files that can't be hashed
    }
}

# العثور على الملفات المتطابقة
foreach ($hash in $hashGroups.Keys) {
    if ($hashGroups[$hash].Count -gt 1) {
        $group = $hashGroups[$hash]
        $sizeMB = [math]::Round($group[0].Length / 1MB, 2)
        
        Write-Host "  Identical files found (Hash: $($hash.Substring(0,8))..., Size: $sizeMB MB each):" -ForegroundColor Red
        foreach ($file in $group) {
            $relativePath = $file.FullName.Replace($projectPath, "").TrimStart('\')
            Write-Host "    - $relativePath" -ForegroundColor White
            
            $identicalFiles += [PSCustomObject]@{
                Path = $relativePath
                FullPath = $file.FullName
                Hash = $hash
                Size = $file.Length
                SizeMB = $sizeMB
            }
        }
        Write-Host ""
    }
}

# البحث عن ملفات تالفة محتملة
Write-Host "`n=== Searching for potentially corrupted files ===" -ForegroundColor Cyan

$corruptedFiles = @()

# ملفات بحجم 0
$zeroSizeFiles = Get-ChildItem -Path $projectPath -Recurse -File | Where-Object { $_.Length -eq 0 }
foreach ($file in $zeroSizeFiles) {
    $relativePath = $file.FullName.Replace($projectPath, "").TrimStart('\')
    Write-Host "  Zero-size file: $relativePath" -ForegroundColor Red
    
    $corruptedFiles += [PSCustomObject]@{
        Path = $relativePath
        FullPath = $file.FullName
        Issue = "Zero size"
        Size = 0
    }
}

# ملفات بامتدادات غريبة أو بدون امتداد
$suspiciousFiles = Get-ChildItem -Path $projectPath -Recurse -File | 
    Where-Object { 
        $_.Extension -eq "" -or 
        $_.Extension -match "\.(tmp|temp|bak|old|~)$" -or
        $_.Name -match "^~.*" -or
        $_.Name -match "\..*\..*\."
    }

foreach ($file in $suspiciousFiles) {
    $relativePath = $file.FullName.Replace($projectPath, "").TrimStart('\')
    $sizeMB = [math]::Round($file.Length / 1MB, 2)
    Write-Host "  Suspicious file: $relativePath ($sizeMB MB)" -ForegroundColor Orange
    
    $corruptedFiles += [PSCustomObject]@{
        Path = $relativePath
        FullPath = $file.FullName
        Issue = "Suspicious extension/name"
        Size = $file.Length
        SizeMB = $sizeMB
    }
}

# عرض الملخص
Write-Host "`n" + "="*60 -ForegroundColor Magenta
Write-Host "SUMMARY" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Magenta

$totalDuplicateSize = ($duplicatesFound | Measure-Object -Property Size -Sum).Sum
$totalIdenticalSize = ($identicalFiles | Measure-Object -Property Size -Sum).Sum
$totalCorruptedSize = ($corruptedFiles | Measure-Object -Property Size -Sum).Sum

Write-Host "Duplicate files found: $($duplicatesFound.Count)" -ForegroundColor Yellow
if ($totalDuplicateSize -gt 0) {
    $dupSizeMB = [math]::Round($totalDuplicateSize / 1MB, 2)
    Write-Host "  Total size: $dupSizeMB MB" -ForegroundColor Yellow
}

Write-Host "Identical content files: $($identicalFiles.Count)" -ForegroundColor Red
if ($totalIdenticalSize -gt 0) {
    $idSizeMB = [math]::Round($totalIdenticalSize / 1MB, 2)
    Write-Host "  Total size: $idSizeMB MB" -ForegroundColor Red
}

Write-Host "Potentially corrupted files: $($corruptedFiles.Count)" -ForegroundColor Orange
if ($totalCorruptedSize -gt 0) {
    $corSizeMB = [math]::Round($totalCorruptedSize / 1MB, 2)
    Write-Host "  Total size: $corSizeMB MB" -ForegroundColor Orange
}

$totalWasteSize = $totalDuplicateSize + $totalIdenticalSize + $totalCorruptedSize
if ($totalWasteSize -gt 0) {
    $wasteSizeMB = [math]::Round($totalWasteSize / 1MB, 2)
    $wasteSizeGB = [math]::Round($totalWasteSize / 1GB, 2)
    Write-Host "`nTotal potential space to free: $wasteSizeMB MB ($wasteSizeGB GB)" -ForegroundColor Cyan
}

Write-Host "="*60 -ForegroundColor Magenta

# اختيار حذف الملفات
if ($duplicatesFound.Count -gt 0 -or $identicalFiles.Count -gt 0 -or $corruptedFiles.Count -gt 0) {
    $deleteChoice = Read-Host "`nDo you want to delete these files? (y/n)"
    if ($deleteChoice -eq 'y' -or $deleteChoice -eq 'Y') {
        Write-Host "`nDeleting files..." -ForegroundColor Yellow
        
        # حذف الملفات المستنسخة
        foreach ($file in $duplicatesFound) {
            try {
                Remove-Item -Path $file.FullPath -Force -ErrorAction SilentlyContinue
                Write-Host "  Deleted duplicate: $($file.Path)" -ForegroundColor Green
            }
            catch {
                Write-Host "  Failed to delete: $($file.Path)" -ForegroundColor Red
            }
        }
        
        # حذف الملفات التالفة
        foreach ($file in $corruptedFiles) {
            try {
                Remove-Item -Path $file.FullPath -Force -ErrorAction SilentlyContinue
                Write-Host "  Deleted corrupted: $($file.Path)" -ForegroundColor Green
            }
            catch {
                Write-Host "  Failed to delete: $($file.Path)" -ForegroundColor Red
            }
        }
        
        # للملفات المتطابقة، احتفظ بواحد فقط
        foreach ($hash in $hashGroups.Keys) {
            if ($hashGroups[$hash].Count -gt 1) {
                $group = $hashGroups[$hash]
                # احتفظ بالأول واحذف الباقي
                for ($i = 1; $i -lt $group.Count; $i++) {
                    try {
                        Remove-Item -Path $group[$i].FullName -Force -ErrorAction SilentlyContinue
                        $relativePath = $group[$i].FullName.Replace($projectPath, "").TrimStart('\')
                        Write-Host "  Deleted identical: $relativePath" -ForegroundColor Green
                    }
                    catch {
                        Write-Host "  Failed to delete: $relativePath" -ForegroundColor Red
                    }
                }
            }
        }
        
        Write-Host "`nCleanup completed!" -ForegroundColor Green
    }
} else {
    Write-Host "`nNo problematic files found. Project is clean!" -ForegroundColor Green
}
