import 'package:cloud_firestore/cloud_firestore.dart';

class ChatRoomModel {
  final String id;
  final String name;
  final String description;
  final String academicYear;
  final bool isGeneral;
  final List<String> members;
  final DateTime? createdAt;
  final String? lastMessage;
  final DateTime? lastMessageTime;

  ChatRoomModel({
    required this.id,
    required this.name,
    required this.description,
    required this.academicYear,
    this.isGeneral = false,
    this.members = const [],
    this.createdAt,
    this.lastMessage,
    this.lastMessageTime,
  });

  factory ChatRoomModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return ChatRoomModel(
      id: doc.id,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      academicYear: data['academicYear'] ?? '',
      isGeneral: data['isGeneral'] ?? false,
      members: List<String>.from(data['members'] ?? []),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate(),
      lastMessage: data['lastMessage'],
      lastMessageTime: (data['lastMessageTime'] as Timestamp?)?.toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'description': description,
      'academicYear': academicYear,
      'isGeneral': isGeneral,
      'members': members,
      'createdAt': createdAt != null ? Timestamp.fromDate(createdAt!) : null,
      'lastMessage': lastMessage,
      'lastMessageTime': lastMessageTime != null ? Timestamp.fromDate(lastMessageTime!) : null,
    };
  }

  ChatRoomModel copyWith({
    String? id,
    String? name,
    String? description,
    String? academicYear,
    bool? isGeneral,
    List<String>? members,
    DateTime? createdAt,
    String? lastMessage,
    DateTime? lastMessageTime,
  }) {
    return ChatRoomModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      academicYear: academicYear ?? this.academicYear,
      isGeneral: isGeneral ?? this.isGeneral,
      members: members ?? this.members,
      createdAt: createdAt ?? this.createdAt,
      lastMessage: lastMessage ?? this.lastMessage,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
    );
  }

  bool isMember(String userId) {
    return members.contains(userId);
  }

  int get memberCount => members.length;
}

class MessageModel {
  final String id;
  final String message;
  final String senderId;
  final String senderName;
  final DateTime? timestamp;
  final MessageType type;
  final String? imageUrl;
  final String? fileUrl;
  final String? fileName;

  MessageModel({
    required this.id,
    required this.message,
    required this.senderId,
    required this.senderName,
    this.timestamp,
    this.type = MessageType.text,
    this.imageUrl,
    this.fileUrl,
    this.fileName,
  });

  factory MessageModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return MessageModel(
      id: doc.id,
      message: data['message'] ?? '',
      senderId: data['senderId'] ?? '',
      senderName: data['senderName'] ?? 'مجهول',
      timestamp: (data['timestamp'] as Timestamp?)?.toDate(),
      type: MessageType.values.firstWhere(
        (e) => e.toString() == 'MessageType.${data['type'] ?? 'text'}',
        orElse: () => MessageType.text,
      ),
      imageUrl: data['imageUrl'],
      fileUrl: data['fileUrl'],
      fileName: data['fileName'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'message': message,
      'senderId': senderId,
      'senderName': senderName,
      'timestamp': timestamp != null ? Timestamp.fromDate(timestamp!) : null,
      'type': type.toString().split('.').last,
      'imageUrl': imageUrl,
      'fileUrl': fileUrl,
      'fileName': fileName,
    };
  }

  MessageModel copyWith({
    String? id,
    String? message,
    String? senderId,
    String? senderName,
    DateTime? timestamp,
    MessageType? type,
    String? imageUrl,
    String? fileUrl,
    String? fileName,
  }) {
    return MessageModel(
      id: id ?? this.id,
      message: message ?? this.message,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      timestamp: timestamp ?? this.timestamp,
      type: type ?? this.type,
      imageUrl: imageUrl ?? this.imageUrl,
      fileUrl: fileUrl ?? this.fileUrl,
      fileName: fileName ?? this.fileName,
    );
  }

  bool get hasImage => imageUrl != null && imageUrl!.isNotEmpty;
  bool get hasFile => fileUrl != null && fileUrl!.isNotEmpty;
  bool get isTextOnly => type == MessageType.text && !hasImage && !hasFile;
}

enum MessageType {
  text,
  image,
  file,
  system,
}

class ChatUser {
  final String id;
  final String name;
  final String? avatar;
  final bool isOnline;
  final DateTime? lastSeen;

  ChatUser({
    required this.id,
    required this.name,
    this.avatar,
    this.isOnline = false,
    this.lastSeen,
  });

  factory ChatUser.fromMap(Map<String, dynamic> map) {
    return ChatUser(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      avatar: map['avatar'],
      isOnline: map['isOnline'] ?? false,
      lastSeen: (map['lastSeen'] as Timestamp?)?.toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'avatar': avatar,
      'isOnline': isOnline,
      'lastSeen': lastSeen != null ? Timestamp.fromDate(lastSeen!) : null,
    };
  }

  ChatUser copyWith({
    String? id,
    String? name,
    String? avatar,
    bool? isOnline,
    DateTime? lastSeen,
  }) {
    return ChatUser(
      id: id ?? this.id,
      name: name ?? this.name,
      avatar: avatar ?? this.avatar,
      isOnline: isOnline ?? this.isOnline,
      lastSeen: lastSeen ?? this.lastSeen,
    );
  }
}
