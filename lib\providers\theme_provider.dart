import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';

class ThemeProvider extends ChangeNotifier {
  bool _isDarkMode = false;
  SharedPreferences? _prefs;

  bool get isDarkMode => _isDarkMode;

  ThemeProvider() {
    _loadThemeFromPrefs();
  }

  // تحميل إعدادات الثيم من التخزين المحلي
  Future<void> _loadThemeFromPrefs() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      _isDarkMode = _prefs?.getBool(AppConfig.prefsKeyTheme) ?? false;
      notifyListeners();
    } catch (e) {
      // في حالة حدوث خطأ، استخدم الوضع الفاتح كافتراضي
      _isDarkMode = false;
      notifyListeners();
    }
  }

  // تغيير الثيم وحفظه في التخزين المحلي
  Future<void> toggleTheme() async {
    try {
      _isDarkMode = !_isDarkMode;
      notifyListeners();
      
      // حفظ الإعداد في التخزين المحلي
      if (_prefs != null) {
        await _prefs!.setBool(AppConfig.prefsKeyTheme, _isDarkMode);
      }
    } catch (e) {
      // في حالة حدوث خطأ، إرجاع الحالة السابقة
      _isDarkMode = !_isDarkMode;
      notifyListeners();
    }
  }

  // تعيين الثيم مباشرة
  Future<void> setTheme(bool isDark) async {
    try {
      if (_isDarkMode != isDark) {
        _isDarkMode = isDark;
        notifyListeners();
        
        // حفظ الإعداد في التخزين المحلي
        if (_prefs != null) {
          await _prefs!.setBool(AppConfig.prefsKeyTheme, _isDarkMode);
        }
      }
    } catch (e) {
      // في حالة حدوث خطأ، لا تغير شيء
      debugPrint('خطأ في تعيين الثيم: $e');
    }
  }

  // الحصول على الثيم الحالي
  ThemeData get currentTheme {
    return _isDarkMode ? _darkTheme : _lightTheme;
  }

  // الثيم الفاتح
  static final ThemeData _lightTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    colorScheme: ColorScheme.fromSeed(
      seedColor: const Color(0xFF6366F1),
      brightness: Brightness.light,
    ),
    scaffoldBackgroundColor: const Color(0xFFF8FAFC),
    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.transparent,
      elevation: 0,
      scrolledUnderElevation: 0,
      iconTheme: IconThemeData(color: Color(0xFF1E293B)),
      titleTextStyle: TextStyle(
        color: Color(0xFF1E293B),
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
    ),
    cardTheme: CardTheme(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: const BorderSide(color: Color(0xFFE2E8F0), width: 1),
      ),
      color: Colors.white,
    ),
    textTheme: const TextTheme(
      bodyLarge: TextStyle(color: Color(0xFF1E293B)),
      bodyMedium: TextStyle(color: Color(0xFF64748B)),
      titleLarge: TextStyle(color: Color(0xFF1E293B)),
      titleMedium: TextStyle(color: Color(0xFF1E293B)),
      titleSmall: TextStyle(color: Color(0xFF64748B)),
    ),
  );

  // الثيم المظلم
  static final ThemeData _darkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    colorScheme: ColorScheme.fromSeed(
      seedColor: const Color(0xFF6366F1),
      brightness: Brightness.dark,
    ),
    scaffoldBackgroundColor: const Color(0xFF0F172A),
    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.transparent,
      elevation: 0,
      scrolledUnderElevation: 0,
      iconTheme: IconThemeData(color: Color(0xFFF1F5F9)),
      titleTextStyle: TextStyle(
        color: Color(0xFFF1F5F9),
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
    ),
    cardTheme: CardTheme(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: const BorderSide(color: Color(0xFF334155), width: 1),
      ),
      color: const Color(0xFF1E293B),
    ),
    textTheme: const TextTheme(
      bodyLarge: TextStyle(color: Color(0xFFF1F5F9)),
      bodyMedium: TextStyle(color: Color(0xFF94A3B8)),
      titleLarge: TextStyle(color: Color(0xFFF1F5F9)),
      titleMedium: TextStyle(color: Color(0xFFF1F5F9)),
      titleSmall: TextStyle(color: Color(0xFF94A3B8)),
    ),
  );
}
