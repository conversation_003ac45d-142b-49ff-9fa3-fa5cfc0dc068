class Subject {
  final String id;
  final String name;
  final String arabicName;
  final int credits;
  final List<String> pdfFiles;
  final String description;
  final int lecturesCount;
  final int examsCount;

  Subject({
    required this.id,
    required this.name,
    required this.arabicName,
    required this.credits,
    required this.pdfFiles,
    required this.description,
    this.lecturesCount = 0,
    this.examsCount = 0,
  });

  factory Subject.fromJson(Map<String, dynamic> json) {
    return Subject(
      id: json['id'],
      name: json['name'],
      arabicName: json['arabicName'],
      credits: json['credits'],
      pdfFiles: List<String>.from(json['pdfFiles']),
      description: json['description'],
      lecturesCount: json['lecturesCount'] ?? 0,
      examsCount: json['examsCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'arabicName': arabicName,
      'credits': credits,
      'pdfFiles': pdfFiles,
      'description': description,
      'lecturesCount': lecturesCount,
      'examsCount': examsCount,
    };
  }
}

class Semester {
  final String id;
  final String name;
  final String arabicName;
  final List<Subject> subjects;

  Semester({
    required this.id,
    required this.name,
    required this.arabicName,
    required this.subjects,
  });

  factory Semester.fromJson(Map<String, dynamic> json) {
    return Semester(
      id: json['id'],
      name: json['name'],
      arabicName: json['arabicName'],
      subjects:
          (json['subjects'] as List)
              .map((subject) => Subject.fromJson(subject))
              .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'arabicName': arabicName,
      'subjects': subjects.map((subject) => subject.toJson()).toList(),
    };
  }
}

class AcademicYear {
  final String id;
  final String name;
  final String arabicName;
  final List<Semester> semesters;
  final String color;
  final String gradientStart;
  final String gradientEnd;

  AcademicYear({
    required this.id,
    required this.name,
    required this.arabicName,
    required this.semesters,
    required this.color,
    required this.gradientStart,
    required this.gradientEnd,
  });

  factory AcademicYear.fromJson(Map<String, dynamic> json) {
    return AcademicYear(
      id: json['id'],
      name: json['name'],
      arabicName: json['arabicName'],
      semesters:
          (json['semesters'] as List)
              .map((semester) => Semester.fromJson(semester))
              .toList(),
      color: json['color'],
      gradientStart: json['gradientStart'],
      gradientEnd: json['gradientEnd'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'arabicName': arabicName,
      'semesters': semesters.map((semester) => semester.toJson()).toList(),
      'color': color,
      'gradientStart': gradientStart,
      'gradientEnd': gradientEnd,
    };
  }
}
