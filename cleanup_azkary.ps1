# Cleanup Azkary project from temporary files
Write-Host "Starting Azkary project cleanup..." -ForegroundColor Green

$projectPath = "C:\Users\<USER>\Documents\augment-projects\azkary\azkary"

if (-not (Test-Path $projectPath)) {
    Write-Host "Azkary project not found at specified path" -ForegroundColor Red
    exit
}

Write-Host "Cleaning project: $projectPath" -ForegroundColor Yellow

# List of folders to delete
$foldersToDelete = @(
    "build",
    ".dart_tool",
    ".packages",
    "ios/Pods",
    "ios/.symlinks",
    "android/.gradle",
    "android/app/build",
    "android/build",
    "windows/build",
    "windows/flutter/ephemeral",
    "linux/build",
    "linux/flutter/ephemeral",
    "macos/build",
    "web/build"
)

# List of files to delete
$filesToDelete = @(
    "pubspec.lock",
    "*.log",
    "*.tmp",
    ".flutter-plugins",
    ".flutter-plugins-dependencies",
    ".metadata"
)

$totalSpaceSaved = 0

# Delete folders
foreach ($folder in $foldersToDelete) {
    $fullPath = Join-Path $projectPath $folder
    if (Test-Path $fullPath) {
        try {
            # Calculate folder size before deletion
            $size = (Get-ChildItem -Path $fullPath -Recurse -ErrorAction SilentlyContinue |
                    Measure-Object -Property Length -Sum).Sum
            if ($size) {
                $totalSpaceSaved += $size
                $sizeMB = [math]::Round($size / 1MB, 2)
                Write-Host "  Deleted folder: $folder ($sizeMB MB)" -ForegroundColor Green
            } else {
                Write-Host "  Deleted folder: $folder" -ForegroundColor Green
            }

            Remove-Item -Path $fullPath -Recurse -Force -ErrorAction SilentlyContinue
        }
        catch {
            Write-Host "  Failed to delete: $folder - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Delete files
foreach ($filePattern in $filesToDelete) {
    $files = Get-ChildItem -Path $projectPath -Name $filePattern -ErrorAction SilentlyContinue
    foreach ($file in $files) {
        $fullPath = Join-Path $projectPath $file
        if (Test-Path $fullPath) {
            try {
                $size = (Get-Item $fullPath).Length
                $totalSpaceSaved += $size

                Remove-Item -Path $fullPath -Force -ErrorAction SilentlyContinue
                Write-Host "  Deleted file: $file" -ForegroundColor Green
            }
            catch {
                Write-Host "  Failed to delete: $file - $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
}

# Display results
$totalSpaceMB = [math]::Round($totalSpaceSaved / 1MB, 2)
$totalSpaceGB = [math]::Round($totalSpaceSaved / 1GB, 2)

Write-Host "`n" + "="*50 -ForegroundColor Magenta
Write-Host "Azkary project cleanup completed!" -ForegroundColor Green
Write-Host "Total space freed: $totalSpaceMB MB ($totalSpaceGB GB)" -ForegroundColor Cyan
Write-Host "="*50 -ForegroundColor Magenta

# Optional: run flutter clean
$runFlutterClean = Read-Host "`nDo you want to run 'flutter clean' on the project? (y/n)"
if ($runFlutterClean -eq 'y' -or $runFlutterClean -eq 'Y') {
    Write-Host "`nRunning flutter clean on Azkary project..." -ForegroundColor Yellow

    Push-Location $projectPath
    try {
        flutter clean
        Write-Host "Azkary project cleaned successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "Failed to run flutter clean: $($_.Exception.Message)" -ForegroundColor Red
    }
    Pop-Location
}

Write-Host "`nAll cleanup operations completed!" -ForegroundColor Green
