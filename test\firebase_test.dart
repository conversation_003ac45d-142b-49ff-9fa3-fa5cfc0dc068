import 'package:flutter_test/flutter_test.dart';
import 'package:sharia_law_app/models/post_model.dart';
import 'package:sharia_law_app/models/chat_model.dart';

void main() {
  group('Firebase Models Tests', () {
    test('PostModel creation and serialization', () {
      final post = PostModel(
        id: 'test_id',
        content: 'Test content',
        authorName: 'Test Author',
        likes: 5,
        likedBy: ['user1', 'user2'],
        comments: [],
        shares: 2,
        createdAt: DateTime.now(),
      );

      expect(post.id, 'test_id');
      expect(post.content, 'Test content');
      expect(post.authorName, 'Test Author');
      expect(post.likes, 5);
      expect(post.shares, 2);

      // Test toMap conversion
      final map = post.toMap();
      expect(map['content'], 'Test content');
      expect(map['authorName'], 'Test Author');
      expect(map['likes'], 5);
    });

    test('CommentModel creation and serialization', () {
      final comment = CommentModel(
        id: 'comment_id',
        comment: 'Test comment',
        authorName: 'Commenter',
        createdAt: DateTime.now(),
      );

      expect(comment.id, 'comment_id');
      expect(comment.comment, 'Test comment');
      expect(comment.authorName, 'Commenter');

      // Test toMap conversion
      final map = comment.toMap();
      expect(map['comment'], 'Test comment');
      expect(map['authorName'], 'Commenter');
    });

    test('PollModel creation and vote calculation', () {
      final poll = PollModel(
        question: 'Test question?',
        options: [
          PollOption(text: 'Option 1', votes: ['user1', 'user2']),
          PollOption(text: 'Option 2', votes: ['user3']),
          PollOption(text: 'Option 3', votes: []),
        ],
        voters: ['user1', 'user2', 'user3'],
      );

      expect(poll.question, 'Test question?');
      expect(poll.options.length, 3);
      expect(poll.totalVotes, 3);
      expect(poll.getOptionPercentage(0), closeTo(66.67, 0.01));
      expect(poll.getOptionPercentage(1), closeTo(33.33, 0.01));
      expect(poll.getOptionPercentage(2), 0.0);
    });

    test('ChatRoomModel creation and member management', () {
      final chatRoom = ChatRoomModel(
        id: 'chat_id',
        name: 'Test Chat',
        description: 'Test Description',
        academicYear: 'السنة الأولى',
        members: ['user1', 'user2'],
        isGeneral: false,
      );

      expect(chatRoom.id, 'chat_id');
      expect(chatRoom.name, 'Test Chat');
      expect(chatRoom.academicYear, 'السنة الأولى');
      expect(chatRoom.memberCount, 2);
      expect(chatRoom.isMember('user1'), true);
      expect(chatRoom.isMember('user3'), false);
      expect(chatRoom.isGeneral, false);
    });

    test('MessageModel creation and type checking', () {
      final message = MessageModel(
        id: 'message_id',
        message: 'Test message',
        senderId: 'sender_id',
        senderName: 'Sender Name',
        type: MessageType.text,
        timestamp: DateTime.now(),
      );

      expect(message.id, 'message_id');
      expect(message.message, 'Test message');
      expect(message.senderId, 'sender_id');
      expect(message.senderName, 'Sender Name');
      expect(message.type, MessageType.text);
      expect(message.isTextOnly, true);
      expect(message.hasImage, false);
      expect(message.hasFile, false);
    });

    test('MessageModel with image', () {
      final message = MessageModel(
        id: 'message_id',
        message: 'Image message',
        senderId: 'sender_id',
        senderName: 'Sender Name',
        type: MessageType.image,
        imageUrl: 'https://example.com/image.jpg',
        timestamp: DateTime.now(),
      );

      expect(message.type, MessageType.image);
      expect(message.hasImage, true);
      expect(message.isTextOnly, false);
      expect(message.imageUrl, 'https://example.com/image.jpg');
    });

    test('MessageModel with file', () {
      final message = MessageModel(
        id: 'message_id',
        message: 'File message',
        senderId: 'sender_id',
        senderName: 'Sender Name',
        type: MessageType.file,
        fileUrl: 'https://example.com/file.pdf',
        fileName: 'document.pdf',
        timestamp: DateTime.now(),
      );

      expect(message.type, MessageType.file);
      expect(message.hasFile, true);
      expect(message.isTextOnly, false);
      expect(message.fileUrl, 'https://example.com/file.pdf');
      expect(message.fileName, 'document.pdf');
    });

    test('ChatUser creation and status', () {
      final user = ChatUser(
        id: 'user_id',
        name: 'User Name',
        isOnline: true,
        lastSeen: DateTime.now(),
      );

      expect(user.id, 'user_id');
      expect(user.name, 'User Name');
      expect(user.isOnline, true);
      expect(user.lastSeen, isNotNull);
    });

    test('PostModel copyWith functionality', () {
      final originalPost = PostModel(
        id: 'original_id',
        content: 'Original content',
        authorName: 'Original Author',
        likes: 0,
      );

      final updatedPost = originalPost.copyWith(
        likes: 5,
        content: 'Updated content',
      );

      expect(updatedPost.id, 'original_id'); // unchanged
      expect(updatedPost.authorName, 'Original Author'); // unchanged
      expect(updatedPost.likes, 5); // changed
      expect(updatedPost.content, 'Updated content'); // changed
    });

    test('Empty poll percentage calculation', () {
      final poll = PollModel(
        question: 'Empty poll?',
        options: [
          PollOption(text: 'Option 1', votes: []),
          PollOption(text: 'Option 2', votes: []),
        ],
        voters: [],
      );

      expect(poll.totalVotes, 0);
      expect(poll.getOptionPercentage(0), 0.0);
      expect(poll.getOptionPercentage(1), 0.0);
    });
  });
}
