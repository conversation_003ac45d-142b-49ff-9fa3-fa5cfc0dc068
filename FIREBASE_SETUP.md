# إعداد Firebase للمشروع

## نظرة عامة
تم تهيئة Firebase بالكامل للمشروع مع الميزات التالية:
- قاعدة البيانات (Firestore)
- التخزين (Storage)
- الإشعارات (Cloud Messaging)
- التحليلات (Analytics)

## الملفات المضافة

### 1. ملفات التكوين
- `lib/config/firebase_config.dart` - إعدادات Firebase الأساسية
- `lib/services/firebase_service.dart` - خدمات Firebase
- `lib/providers/firebase_provider.dart` - إدارة حالة Firebase
- `lib/models/post_model.dart` - نموذج المنشورات
- `lib/models/chat_model.dart` - نموذج المحادثات

### 2. ملف<PERSON><PERSON> Android
- `android/app/google-services.json` - إعدادات Firebase للأندرويد
- تم تحديث `android/app/build.gradle.kts`
- تم تحديث `android/build.gradle.kts`
- تم تحديث `android/app/src/main/AndroidManifest.xml`

### 3. ملفات iOS
- `ios/Runner/GoogleService-Info.plist` - إعدادات Firebase للـ iOS
- تم تحديث `ios/Runner/Info.plist`

### 4. ملفات الويب
- `web/firebase-config.js` - إعدادات Firebase للويب
- `web/firebase-messaging-sw.js` - Service Worker للإشعارات
- تم تحديث `web/index.html`

## التبعيات المضافة
```yaml
# Firebase
firebase_core: ^3.6.0
firebase_auth: ^5.3.1
cloud_firestore: ^5.4.3
firebase_storage: ^12.3.2
firebase_messaging: ^15.1.3
firebase_analytics: ^11.3.3
```

## الميزات المتاحة

### 1. إدارة المنشورات
- إضافة منشورات جديدة
- رفع الصور والملفات
- نظام الإعجابات والتعليقات
- الاستطلاعات
- المنشورات المجهولة

### 2. نظام المحادثات
- غرف محادثة للسنوات الدراسية
- محادثة عامة
- إرسال الرسائل
- الانضمام ومغادرة الغرف

### 3. الإشعارات
- إشعارات فورية
- إشعارات في الخلفية
- دعم جميع المنصات

### 4. التخزين
- رفع الصور
- رفع الملفات (PDF, Excel)
- تخزين آمن في السحابة

## كيفية الاستخدام

### 1. تهيئة Firebase في التطبيق
```dart
import 'package:provider/provider.dart';
import 'providers/firebase_provider.dart';

// في main.dart
MultiProvider(
  providers: [
    ChangeNotifierProvider(create: (_) => FirebaseProvider()),
    // ... providers أخرى
  ],
  child: MyApp(),
)
```

### 2. استخدام FirebaseProvider
```dart
// الحصول على المنشورات
final firebaseProvider = Provider.of<FirebaseProvider>(context);
firebaseProvider.loadPosts();

// إضافة منشور جديد
await firebaseProvider.addPost(
  content: 'محتوى المنشور',
  isAnonymous: false,
);

// إضافة إعجاب
await firebaseProvider.likePost(postId);
```

### 3. استخدام المحادثات
```dart
// تحميل غرف المحادثة
firebaseProvider.loadChatRooms();

// الانضمام لغرفة محادثة
await firebaseProvider.joinChatRoom(chatId);

// إرسال رسالة
await firebaseProvider.sendMessage(chatId, message);
```

## إعدادات Firebase Console

### 1. قاعدة البيانات (Firestore)
المجموعات المطلوبة:
- `posts` - المنشورات
- `chats` - غرف المحادثة
- `users` - المستخدمين

### 2. قواعد الأمان
```javascript
// Firestore Rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Posts collection
    match /posts/{postId} {
      allow read, write: if true; // يمكن تخصيصها حسب الحاجة
    }
    
    // Chats collection
    match /chats/{chatId} {
      allow read, write: if true;
      
      match /messages/{messageId} {
        allow read, write: if true;
      }
    }
    
    // Users collection
    match /users/{userId} {
      allow read, write: if true;
    }
  }
}
```

### 3. Storage Rules
```javascript
// Storage Rules
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if true; // يمكن تخصيصها حسب الحاجة
    }
  }
}
```

## الخطوات التالية

1. **تشغيل التطبيق**: `flutter run`
2. **اختبار الميزات**: تأكد من عمل جميع الميزات
3. **تخصيص القواعد**: حدد قواعد الأمان حسب احتياجاتك
4. **إضافة المصادقة**: يمكن إضافة نظام تسجيل الدخول لاحقاً

## ملاحظات مهمة

- تأكد من تفعيل جميع الخدمات في Firebase Console
- قم بتحديث قواعد الأمان قبل النشر
- اختبر الإشعارات على الأجهزة الحقيقية
- تأكد من صحة package names في جميع المنصات

## الدعم والمساعدة

في حالة وجود مشاكل:
1. تحقق من Firebase Console
2. راجع logs التطبيق
3. تأكد من صحة التكوين
4. اختبر على أجهزة مختلفة
