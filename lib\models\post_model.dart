import 'package:cloud_firestore/cloud_firestore.dart';

class PostModel {
  final String id;
  final String content;
  final String authorName;
  final String? imageUrl;
  final Map<String, dynamic>? pollData;
  final List<String> attachments;
  final bool isAnonymous;
  final int likes;
  final List<String> likedBy;
  final List<CommentModel> comments;
  final int shares;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  PostModel({
    required this.id,
    required this.content,
    required this.authorName,
    this.imageUrl,
    this.pollData,
    this.attachments = const [],
    this.isAnonymous = false,
    this.likes = 0,
    this.likedBy = const [],
    this.comments = const [],
    this.shares = 0,
    this.createdAt,
    this.updatedAt,
  });

  factory PostModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return PostModel(
      id: doc.id,
      content: data['content'] ?? '',
      authorName: data['authorName'] ?? 'مجهول',
      imageUrl: data['imageUrl'],
      pollData: data['pollData'],
      attachments: List<String>.from(data['attachments'] ?? []),
      isAnonymous: data['isAnonymous'] ?? false,
      likes: data['likes'] ?? 0,
      likedBy: List<String>.from(data['likedBy'] ?? []),
      comments:
          (data['comments'] as List<dynamic>? ?? [])
              .map((comment) => CommentModel.fromMap(comment))
              .toList(),
      shares: data['shares'] ?? 0,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'content': content,
      'authorName': authorName,
      'imageUrl': imageUrl,
      'pollData': pollData,
      'attachments': attachments,
      'isAnonymous': isAnonymous,
      'likes': likes,
      'likedBy': likedBy,
      'comments': comments.map((comment) => comment.toMap()).toList(),
      'shares': shares,
      'createdAt': createdAt != null ? Timestamp.fromDate(createdAt!) : null,
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  PostModel copyWith({
    String? id,
    String? content,
    String? authorName,
    String? imageUrl,
    Map<String, dynamic>? pollData,
    List<String>? attachments,
    bool? isAnonymous,
    int? likes,
    List<String>? likedBy,
    List<CommentModel>? comments,
    int? shares,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PostModel(
      id: id ?? this.id,
      content: content ?? this.content,
      authorName: authorName ?? this.authorName,
      imageUrl: imageUrl ?? this.imageUrl,
      pollData: pollData ?? this.pollData,
      attachments: attachments ?? this.attachments,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      likes: likes ?? this.likes,
      likedBy: likedBy ?? this.likedBy,
      comments: comments ?? this.comments,
      shares: shares ?? this.shares,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class CommentModel {
  final String id;
  final String comment;
  final String authorName;
  final DateTime? createdAt;

  CommentModel({
    required this.id,
    required this.comment,
    required this.authorName,
    this.createdAt,
  });

  factory CommentModel.fromMap(Map<String, dynamic> map) {
    return CommentModel(
      id: map['id'] ?? '',
      comment: map['comment'] ?? '',
      authorName: map['authorName'] ?? 'مجهول',
      createdAt: (map['createdAt'] as Timestamp?)?.toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'comment': comment,
      'authorName': authorName,
      'createdAt': createdAt != null ? Timestamp.fromDate(createdAt!) : null,
    };
  }
}

class PollModel {
  final String question;
  final List<PollOption> options;
  final List<String> voters;
  final DateTime? expiresAt;

  PollModel({
    required this.question,
    required this.options,
    this.voters = const [],
    this.expiresAt,
  });

  factory PollModel.fromMap(Map<String, dynamic> map) {
    return PollModel(
      question: map['question'] ?? '',
      options:
          (map['options'] as List<dynamic>? ?? [])
              .map((option) => PollOption.fromMap(option))
              .toList(),
      voters: List<String>.from(map['voters'] ?? []),
      expiresAt: (map['expiresAt'] as Timestamp?)?.toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'question': question,
      'options': options.map((option) => option.toMap()).toList(),
      'voters': voters,
      'expiresAt': expiresAt != null ? Timestamp.fromDate(expiresAt!) : null,
    };
  }

  int get totalVotes =>
      options.fold(0, (total, option) => total + option.votes.length);

  double getOptionPercentage(int optionIndex) {
    if (totalVotes == 0) return 0.0;
    return (options[optionIndex].votes.length / totalVotes) * 100;
  }
}

class PollOption {
  final String text;
  final List<String> votes;

  PollOption({required this.text, this.votes = const []});

  factory PollOption.fromMap(Map<String, dynamic> map) {
    return PollOption(
      text: map['text'] ?? '',
      votes: List<String>.from(map['votes'] ?? []),
    );
  }

  Map<String, dynamic> toMap() {
    return {'text': text, 'votes': votes};
  }
}
